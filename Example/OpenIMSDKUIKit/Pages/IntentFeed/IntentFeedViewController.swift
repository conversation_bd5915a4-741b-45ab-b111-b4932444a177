import UIKit
import SnapKit
import RxSwift
import OUICore
import OUIIM
import OpenIMCore
import Localize_Swift

// MARK: - 使用与项目一致的本地化方法并添加诊断
extension String {
    var localized: String {
        // 尝试不同的本地化方法并记录结果
        let mainBundle = Bundle.main
        let mainBundlePath = mainBundle.bundlePath
        
        let innerLocalizedResult = self.innerLocalized()
        
        let appBundlePath = Bundle.main.bundlePath
        let appEnPath = appBundlePath + "/en.lproj"
        let appZhPath = appBundlePath + "/zh-Hans.lproj"
        
        print("\n🔍 本地化调试 - 键: '\(self)'")
        print("📱 当前语言: \(Localize.currentLanguage())")
        print("📦 主Bundle路径: \(mainBundlePath)")
        print("🈚️ innerLocalized结果: '\(innerLocalizedResult)'")
        print("📦 检查是否存在语言资源路径:")
        print("   - 英文: \(FileManager.default.fileExists(atPath: appEnPath) ? "存在✅" : "不存在❌")")
        print("   - 中文: \(FileManager.default.fileExists(atPath: appZhPath) ? "存在✅" : "不存在❌")")
        
        // 尝试直接从Main Bundle获取
        let mainBundleResult = NSLocalizedString(self, tableName: nil, bundle: Bundle.main, value: self, comment: "")
        print("📦 Main Bundle结果: '\(mainBundleResult)'")
        
        // 尝试使用Localize-Swift的方法
        let localizeBundleResult = self.localized(in: Bundle.main)
        print("📦 Localize-Swift结果: '\(localizeBundleResult)'")
        
        // 最后检查OpenIMSDKUIKit Bundle
        if let openIMUIKitBundlePath = Bundle.main.path(forResource: "OpenIMSDKUIKit", ofType: "bundle"),
           let openIMUIKitBundle = Bundle(path: openIMUIKitBundlePath) {
            let openIMResult = NSLocalizedString(self, tableName: nil, bundle: openIMUIKitBundle, value: self, comment: "")
            print("📦 OpenIMUIKit Bundle结果: '\(openIMResult)'")
        }
        
        // 打印当前App的所有可用资源bundle
        print("📦 当前App的所有资源bundle:")
        if let resourcePath = Bundle.main.resourcePath {
            do {
                let contents = try FileManager.default.contentsOfDirectory(atPath: resourcePath)
                let bundles = contents.filter { $0.hasSuffix(".bundle") }
                for bundle in bundles {
                    print("   - \(bundle)")
                }
            } catch {
                print("   无法列出资源: \(error.localizedDescription)")
            }
        }
        
        return localizeBundleResult  // 使用Localize-Swift的方法替代innerLocalized
    }
    
    func localizedFormat(_ arguments: CVarArg...) -> String {
        let localizedFormat = self.localized
        return String(format: localizedFormat, arguments: arguments)
    }
}

// MARK: - Models
struct FeedResponse: Codable {
    let status: String
    let posts: [FeedPost]
    let page: Int
    let pageSize: Int
    
    enum CodingKeys: String, CodingKey {
        case status
        case posts
        case page
        case pageSize = "page_size"
    }
}

struct FeedPost: Codable {
    let id: Int
    let publisherId: Int
    let authorName: String
    let publishTime: String
    let category: String
    let content: String
    var images: String?
    var isLiked: Int
    var likeCount: Int
    var isFavorited: Int
    var favoriteCount: Int
    let repostCount: Int
    var commentCount: Int
    
    // 意图相关的字段
    let intent: String?
    let intentCategory: String?
    let matchScore: Double?
    let matchReason: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case publisherId = "publisher_id"
        case authorName = "author_name"
        case publishTime = "publish_time"
        case category
        case content
        case images
        case isLiked = "is_liked"
        case likeCount = "like_count"
        case isFavorited = "is_favorited"
        case favoriteCount = "favorite_count"
        case repostCount = "repost_count"
        case commentCount = "comment_count"
        case intent
        case intentCategory = "intent_category"
        case matchScore = "match_score"
        case matchReason = "match_reason"
    }
    
    init(id: Int, publisherId: Int, authorName: String, publishTime: String, category: String, content: String, images: String?, isLiked: Int, likeCount: Int, isFavorited: Int, favoriteCount: Int, repostCount: Int, commentCount: Int = 0, intent: String? = nil, intentCategory: String? = nil, matchScore: Double? = nil, matchReason: String? = nil) {
        self.id = id
        self.publisherId = publisherId
        self.authorName = authorName
        self.publishTime = publishTime
        self.category = category
        self.content = content
        self.images = images
        self.isLiked = isLiked
        self.likeCount = likeCount
        self.isFavorited = isFavorited
        self.favoriteCount = favoriteCount
        self.repostCount = repostCount
        self.commentCount = commentCount
        self.intent = intent
        self.intentCategory = intentCategory
        self.matchScore = matchScore
        self.matchReason = matchReason
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        publisherId = try container.decode(Int.self, forKey: .publisherId)
        authorName = try container.decode(String.self, forKey: .authorName)
        publishTime = try container.decode(String.self, forKey: .publishTime)
        category = try container.decode(String.self, forKey: .category)
        content = try container.decode(String.self, forKey: .content)
        images = try container.decodeIfPresent(String.self, forKey: .images)
        isLiked = try container.decode(Int.self, forKey: .isLiked)
        likeCount = try container.decode(Int.self, forKey: .likeCount)
        isFavorited = try container.decode(Int.self, forKey: .isFavorited)
        favoriteCount = try container.decode(Int.self, forKey: .favoriteCount)
        repostCount = try container.decode(Int.self, forKey: .repostCount)
        commentCount = try container.decodeIfPresent(Int.self, forKey: .commentCount) ?? 0
        
        // 解码意图相关的字段
        intent = try container.decodeIfPresent(String.self, forKey: .intent)
        intentCategory = try container.decodeIfPresent(String.self, forKey: .intentCategory)
        matchScore = try container.decodeIfPresent(Double.self, forKey: .matchScore)
        matchReason = try container.decodeIfPresent(String.self, forKey: .matchReason)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(publisherId, forKey: .publisherId)
        try container.encode(authorName, forKey: .authorName)
        try container.encode(publishTime, forKey: .publishTime)
        try container.encode(category, forKey: .category)
        try container.encode(content, forKey: .content)
        try container.encodeIfPresent(images, forKey: .images)
        try container.encode(isLiked, forKey: .isLiked)
        try container.encode(likeCount, forKey: .likeCount)
        try container.encode(isFavorited, forKey: .isFavorited)
        try container.encode(favoriteCount, forKey: .favoriteCount)
        try container.encode(repostCount, forKey: .repostCount)
        try container.encode(commentCount, forKey: .commentCount)
        
        // 编码意图相关的字段
        try container.encodeIfPresent(intent, forKey: .intent)
        try container.encodeIfPresent(intentCategory, forKey: .intentCategory)
        try container.encodeIfPresent(matchScore, forKey: .matchScore)
        try container.encodeIfPresent(matchReason, forKey: .matchReason)
    }
    
    // 计算属性，用于显示
    var publisherName: String {
        return authorName
    }
    
    // 修改图片URL解析逻辑
    var imageURLs: [URL]? {
        // 如果是意图动态，直接使用 images 数组
        if intent != nil {
            if let images = images as? [String] {
                return images.compactMap { URL(string: $0) }
            } else if let imagesString = images,
                      let imageData = imagesString.data(using: .utf8),
                      let imageArray = try? JSONDecoder().decode([String].self, from: imageData) {
                return imageArray.compactMap { URL(string: $0) }
            }
            return nil
        }
        
        // 订阅动态使用原有的解析逻辑
        if let imagesString = images,
           let imageData = imagesString.data(using: .utf8),
           let imageArray = try? JSONDecoder().decode([String].self, from: imageData) {
            return imageArray.compactMap { imagePath in
                URL(string: "\(GlobalConfig.apiBaseURL)/images/\(imagePath)")
            }
        }
        return nil
    }
    
    // 格式化发布时间
    var formattedPublishTime: String {
        // 这里可以添加时间格式化逻辑
        return publishTime
    }
    
    // 修改 with 方法以支持更新 images
    func with(
        isLiked: Int? = nil,
        likeCount: Int? = nil,
        isFavorited: Int? = nil,
        favoriteCount: Int? = nil,
        images: String? = nil
    ) -> FeedPost {
        var copy = self
        if let isLiked = isLiked {
            copy.isLiked = isLiked
        }
        if let likeCount = likeCount {
            copy.likeCount = likeCount
        }
        if let isFavorited = isFavorited {
            copy.isFavorited = isFavorited
        }
        if let favoriteCount = favoriteCount {
            copy.favoriteCount = favoriteCount
        }
        if let images = images {
            copy.images = images
        }
        return copy
    }
    
    func toContentDetail() -> ContentDetail {
        // 处理图片数组
        var imageArray: [String]? = nil
        if let imagesString = images {
            // 尝试解析JSON格式的图片数组
            if let data = imagesString.data(using: .utf8),
               let parsedArray = try? JSONDecoder().decode([String].self, from: data) {
                imageArray = parsedArray
            } else {
                // 如果不是JSON格式，尝试按逗号分隔
                imageArray = imagesString.components(separatedBy: ",").filter { !$0.isEmpty }
            }
        }
        
        return ContentDetail(
            id: id,
            publisherId: publisherId,
            publisherName: authorName,
            publishTime: publishTime,
            content: content,
            images: imageArray,
            likeCount: likeCount,
            favoriteCount: favoriteCount,
            repostCount: repostCount,
            isLiked: isLiked == 1,
            isFavorited: isFavorited == 1,
            isReposted: false,
            commentCount: commentCount
        )
    }
}

struct StructuredContent: Codable {
    let category: String
    let type: String
    let `struct`: [String: String]
}

// 自定义字母C加载视图
class LetterCLoadingView: UIView {
    private var letterLabel: UILabel?
    private var isAnimating = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        setupLetterC()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupLetterC() {
        // 创建字母C标签
        letterLabel = UILabel()
        letterLabel?.text = "C"
        letterLabel?.textColor = UIColor.black
        letterLabel?.font = UIFont(name: "Avenir-Heavy", size: 20) ?? UIFont.boldSystemFont(ofSize: 20)
        letterLabel?.textAlignment = .center
        letterLabel?.alpha = 0

        // 确保标签不被裁剪
        letterLabel?.clipsToBounds = false
        letterLabel?.backgroundColor = UIColor.clear

        if let label = letterLabel {
            addSubview(label)
            layoutLetterC()
        }
    }

    private func layoutLetterC() {
        guard let letterLabel = letterLabel else { return }

        // 让标签自动调整大小
        letterLabel.sizeToFit()

        // 居中显示
        letterLabel.center = CGPoint(x: bounds.width / 2, y: bounds.height / 2)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        layoutLetterC()
    }
    

    
    func startAnimating() {
        guard !isAnimating, let letterLabel = letterLabel else { return }
        isAnimating = true

        // 字母C的闪烁动画
        let pulseAnimation = CAKeyframeAnimation(keyPath: "opacity")
        pulseAnimation.values = [0.6, 1.0, 0.6]
        pulseAnimation.keyTimes = [0.0, 0.5, 1.0]
        pulseAnimation.duration = 0.8
        pulseAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        pulseAnimation.repeatCount = .infinity

        // 轻微的缩放效果
        let scaleAnimation = CAKeyframeAnimation(keyPath: "transform.scale")
        scaleAnimation.values = [1.0, 1.15, 1.0]
        scaleAnimation.keyTimes = [0.0, 0.5, 1.0]
        scaleAnimation.duration = 0.8
        scaleAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        scaleAnimation.repeatCount = .infinity

        letterLabel.layer.add(pulseAnimation, forKey: "letterPulse")
        letterLabel.layer.add(scaleAnimation, forKey: "letterScale")
    }
    
    func stopAnimating() {
        guard isAnimating else { return }
        isAnimating = false

        letterLabel?.layer.removeAllAnimations()
        letterLabel?.alpha = 0
    }
}

// 1. 首先定义请求模型
struct LikeRequest: Codable {
    let postId: Int
    let userId: Int
    let userName: String
    
    enum CodingKeys: String, CodingKey {
        case postId = "post_id"
        case userId = "user_id"
        case userName = "user_name"
    }
}

struct UnlikeRequest: Codable {
    let postId: Int
    let userId: Int
    
    enum CodingKeys: String, CodingKey {
        case postId = "post_id"
        case userId = "user_id"
    }
}

// 添加响应模型
struct LikeResponse: Codable {
    let status: String
    let message: String
}

// 2. 添加收藏请求模型
struct FavoriteRequest: Codable {
    let postId: Int
    let userId: Int
    let userName: String
    
    enum CodingKeys: String, CodingKey {
        case postId = "post_id"
        case userId = "user_id"
        case userName = "user_name"
    }
}

struct UnfavoriteRequest: Codable {
    let postId: Int
    let userId: Int
    
    enum CodingKeys: String, CodingKey {
        case postId = "post_id"
        case userId = "user_id"
    }
}

// 添加响应模型
struct FavoriteResponse: Codable {
    let status: String
    let message: String
}

// MARK: - Intent Feed Models
struct IntentFeedResponse: Codable {
    let code: Int
    let message: String
    let data: IntentFeedData?
    
    enum CodingKeys: String, CodingKey {
        case code
        case message
        case data
    }
}

// MARK: - IntentFeedData
struct IntentFeedData: Codable {
    let list: [IntentItem]
    let page: Int
    let pageSize: Int
    let total: Int
    
    enum CodingKeys: String, CodingKey {
        case list
        case page
        case pageSize = "page_size"
        case total
    }
}

struct IntentItem: Codable {
    let intent: IntentInfo
    let match: MatchInfo
    let content: ContentInfo
    
    enum CodingKeys: String, CodingKey {
        case intent
        case match
        case content
    }
    
    func toFeedPost() -> FeedPost {
        // 处理图片数组
        let imagesJsonString: String?
        if let images = content.images, !images.isEmpty {
            imagesJsonString = try? String(data: JSONEncoder().encode(images), encoding: .utf8)
        } else {
            imagesJsonString = nil
        }
        
        // 获取发布者ID和名称
        let publisherId: Int
        let publisherName: String
        
        if let publisher = content.publisher {
            publisherId = publisher.id ?? 0
            publisherName = publisher.name
        } else if let source = content.source {
            publisherId = source.id ?? 0
            publisherName = source.name ?? "未知来源".localized
        } else {
            publisherId = intent.intentId
            publisherName = "未知来源".localized
        }
        
        // 从content对象中获取点赞和收藏状态
        let isLiked = content.isLiked ? 1 : 0
        let isFavorited = content.isFavorited ? 1 : 0
        
        return FeedPost(
            id: content.unifiedId,  // 使用正确的内容ID
            publisherId: publisherId, // 使用上面确定的发布者ID
            authorName: publisherName,
            publishTime: content.publishTime,
            category: content.type,
            content: content.content,
            images: imagesJsonString,
            isLiked: isLiked,
            likeCount: content.stats.likeCount,
            isFavorited: isFavorited,
            favoriteCount: content.stats.favoriteCount,
            repostCount: content.stats.repostCount,
            commentCount: content.stats.commentCount,  // 添加评论数
            intent: intent.content,
            intentCategory: content.type,
            matchScore: match.score,
            matchReason: match.reason
        )
    }
}

struct IntentInfo: Codable {
    let intentId: Int
    let content: String
    let validityPeriod: String
    let timeType: String
    
    enum CodingKeys: String, CodingKey {
        case intentId = "intent_id"
        case content
        case validityPeriod = "validity_period"
        case timeType = "time_type"
    }
}

struct MatchInfo: Codable {
    let matchId: Int
    let score: Double
    let reason: String
    let time: String
    
    enum CodingKeys: String, CodingKey {
        case matchId = "match_id"
        case score
        case reason
        case time
    }
}

struct ContentInfo: Codable {
    let unifiedId: Int
    let type: String
    let publishTime: String
    let content: String
    let images: [String]?
    let location: LocationInfo?
    let stats: StatsInfo
    let source: SourceInfo?
    let title: String?
    let publisher: PublisherInfo?
    let isLiked: Bool
    let isFavorited: Bool
    
    enum CodingKeys: String, CodingKey {
        case unifiedId = "unified_id"
        case type
        case publishTime = "publish_time"
        case content
        case images
        case location
        case stats
        case source
        case title
        case publisher
        case isLiked = "is_liked"
        case isFavorited = "is_favorited"
    }
}

struct LocationInfo: Codable {
    let text: String?   // 改为可选，防止后端返回 null 导致解码失败
    let coordinates: Coordinates?
    
    enum CodingKeys: String, CodingKey {
        case text
        case coordinates
    }
}

struct Coordinates: Codable {
    let x: Double?
    let y: Double?
    
    enum CodingKeys: String, CodingKey {
        case x
        case y
    }
}

struct StatsInfo: Codable {
    let likeCount: Int
    let favoriteCount: Int
    let repostCount: Int
    let commentCount: Int
    
    enum CodingKeys: String, CodingKey {
        case likeCount = "like_count"
        case favoriteCount = "favorite_count"
        case repostCount = "repost_count"
        case commentCount = "comment_count"
    }
}

struct SourceInfo: Codable {
    let url: String
    let name: String?   // 改为可选，兼容 null
    let id: Int?
}

struct PublisherInfo: Codable {
    let id: Int?   // 改为可选，防止后端返回 null 导致解码失败
    let name: String
}

class IntentFeedViewController: UIViewController {
    
    // MARK: - Properties
    private var feeds: [FeedPost] = []
    private var currentPage = 1
    private var isLoading = false
    private let intentImageCache = NSCache<NSString, UIImage>()
    private let subscriptionImageCache = NSCache<NSString, UIImage>()
    private var currentUserId: Int = 0
    private var currentUserName: String = ""
    
    // MARK: - UI Components
    private lazy var segmentedControl: UISegmentedControl = {
        let control = UISegmentedControl(items: ["意图动态".localized, "订阅动态".localized])
        control.selectedSegmentIndex = 0
        control.backgroundColor = .clear
        
        // 移除默认的分段控制器样式
        control.setBackgroundImage(UIImage(), for: .normal, barMetrics: .default)
        control.setDividerImage(UIImage(), forLeftSegmentState: .normal, rightSegmentState: .normal, barMetrics: .default)
        
        // 设置文字样式和大小 - 使用Avenir字体
        control.setTitleTextAttributes([
            .foregroundColor: UIColor.gray,
            .font: UIFont(name: "Avenir-Book", size: 14) ?? UIFont.systemFont(ofSize: 14)
        ], for: .normal)
        
        control.setTitleTextAttributes([
            .foregroundColor: UIColor.black,
            .font: UIFont(name: "Avenir-Medium", size: 14) ?? UIFont.systemFont(ofSize: 14, weight: .semibold)
        ], for: .selected)
        
        return control
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.register(FeedCell.self, forCellReuseIdentifier: "FeedCell")
        table.delegate = self
        table.dataSource = self
        table.separatorStyle = .none
        table.backgroundColor = .white
        return table
    }()
    
    // MARK: - Underline
    private var underlineView: UIView?
    
    // MARK: - Loading View
    private lazy var loadingView: LetterCLoadingView = {
        let view = LetterCLoadingView(frame: CGRect(x: 0, y: 0, width: 50, height: 50))
        view.backgroundColor = .clear
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        // 设置导航栏为透明
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        // 进一步减小导航栏的高度
        navigationController?.navigationBar.frame.size.height = 16 // 减小到原来的一半
        
        // 设置标题
        let currentLanguage = Localize.currentLanguage()
        self.title = currentLanguage == "en" ? "Chill Moment" : "Chill时刻"
        
        // 打印当前语言信息，用于调试
        print("\n=== IntentFeedViewController 语言信息 ===")
        print("当前语言: \(Localize.currentLanguage())")
        print("系统语言: \(Locale.current.languageCode ?? "unknown")")
        print("可用语言: \(Localize.availableLanguages())")
        print("=====================================\n")

        setupUI()
        setupGestures()

        // 简单设置用户ID并直接加载数据（避免额外的网络请求）
        if !IMController.shared.uid.isEmpty {
            currentUserId = Int(IMController.shared.uid) ?? 0
        }
        loadFeeds()
        
        // 立即更新本地化文本
        updateLocalizedTextForCurrentLanguage()
        
        // 添加分段控制器的点击事件
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        
        // 添加语言变更通知的观察者
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLanguageChange),
            name: NSNotification.Name("RefreshUIForLanguageChange"),
            object: nil
        )
        
        // 也监听Localize_Swift自带的语言变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLanguageChange), 
            name: NSNotification.Name(LCLLanguageChangeNotification),
            object: nil
        )
    }
    
    // 新增方法：根据当前语言更新本地化文本
    private func updateLocalizedTextForCurrentLanguage() {
        print("\n🔄 主动更新本地化文本")
        print("🌐 当前语言: \(Localize.currentLanguage())")
        
        // 更新分段控制器
        segmentedControl.setTitle("意图动态".localized, forSegmentAt: 0)
        segmentedControl.setTitle("订阅动态".localized, forSegmentAt: 1)
        
        // 更新标题和TabBar项目
        let tabTitle = "Chill时刻".localized
        self.title = tabTitle
        tabBarItem.title = tabTitle
        
        // 如果有空数据显示，也更新
        if let backgroundView = tableView.backgroundView as? UILabel {
            if segmentedControl.selectedSegmentIndex == 0 {
                backgroundView.text = "暂无意图动态".localized
            } else {
                backgroundView.text = "暂无订阅动态".localized
            }
        }
        
        // 刷新表格以更新所有单元格中的本地化文本
        tableView.reloadData()
        
        print("✅ 主动更新本地化文本完成\n")
    }
    
    deinit {
        // 移除观察者
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 在页面即将显示时设置导航栏透明
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 在页面即将消失时恢复导航栏默认样式
        navigationController?.navigationBar.setBackgroundImage(nil, for: .default)
        navigationController?.navigationBar.shadowImage = nil
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 只在第一次布局时设置下划线位置
        if !isUnderlinePositionInitialized {
            updateUnderlinePosition()
            isUnderlinePositionInitialized = true
        }
    }
    

    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .white
        
        // 设置分段控制器位置
        view.addSubview(segmentedControl)
        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.centerX.equalToSuperview()  // 居中对齐
            make.width.equalTo(300)  // 增加宽度，确保完整显示"Subscription Feed"
            make.height.equalTo(44)
        }
        
        // 添加下划线
        let underlineView = UIView()
        underlineView.backgroundColor = .black
        view.addSubview(underlineView)
        self.underlineView = underlineView
        
        // 设置下划线约束
        let segmentWidth = segmentedControl.frame.width / CGFloat(segmentedControl.numberOfSegments)
        // 根据选中的选项设置下划线宽度 - 进一步减小宽度
        let underlineWidth: CGFloat = segmentedControl.selectedSegmentIndex == 0 ? 25 : 35
        
        underlineView.snp.makeConstraints { make in
            make.bottom.equalTo(segmentedControl.snp.bottom)
            make.height.equalTo(2) // 下划线高度
            make.width.equalTo(underlineWidth)
            make.centerX.equalTo(segmentedControl.snp.left).offset(segmentWidth / 2 + CGFloat(segmentedControl.selectedSegmentIndex) * segmentWidth)
        }
        
        // 其他UI设置保持不变
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 确保TabBar项目使用本地化文本
        setupTabBarItem()
    }
    
    private func setupGestures() {
        let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(handleSwipe(_:)))
        swipeLeft.direction = .left
        view.addGestureRecognizer(swipeLeft)
        
        let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(handleSwipe(_:)))
        swipeRight.direction = .right
        view.addGestureRecognizer(swipeRight)
        
        // 确保手势识别器不会被其他视图阻止
        view.isUserInteractionEnabled = true
        tableView.isUserInteractionEnabled = true
    }
    
    // MARK: - Data Loading
    private func loadFeeds() {
        guard !isLoading else { return }
        isLoading = true
        
        // 显示加载动画
        view.addSubview(loadingView)
        loadingView.center = view.center
        loadingView.startAnimating()
        
        // 根据选中的分段控制器选择加载不同的数据
        if segmentedControl.selectedSegmentIndex == 0 {
            loadIntentFeeds()
        } else {
            loadSubscriptionFeeds()
        }
    }
    
    private func loadIntentFeeds() {
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/user_intent_content?user_id=\(currentUserId)&page=\(currentPage)&page_size=20") else {
            handleLoadingCompletion()
            return
        }

        print("DEBUG: Loading intent feeds from URL: \(url)")
        print("DEBUG: 当前用户ID: \(currentUserId), 页码: \(currentPage)")
        
        // 创建URLRequest并设置更长的超时时间
        var request = URLRequest(url: url)
        request.timeoutInterval = 30 // 增加超时时间到30秒
        
        // 使用重试机制
        func performRequest(retryCount: Int = 0, maxRetries: Int = 2) {
            URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    // 检查是否需要重试
                    if let error = error {
                        print("DEBUG: Error fetching intent feeds: \(error.localizedDescription)")
                        
                        // 如果是网络错误且还有重试次数，则重试
                        let isNetworkError = (error as NSError).domain == NSURLErrorDomain && 
                                            [NSURLErrorTimedOut, NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost].contains((error as NSError).code)
                        
                        if isNetworkError && retryCount < maxRetries {
                            print("DEBUG: 重试网络请求，第\(retryCount + 1)次...")
                            // 延迟1秒后重试
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                performRequest(retryCount: retryCount + 1, maxRetries: maxRetries)
                            }
                            return
                        }
                        
                        self.handleLoadingCompletion()
                        self.showErrorMessage("网络连接问题，请检查网络后重试".localized)
                        return
                    }
                    
                    guard let data = data else {
                        self.handleLoadingCompletion()
                        self.showErrorMessage("未收到数据".localized)
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode != 200 {
                        self.handleLoadingCompletion()
                        self.showErrorMessage("服务器返回错误: %d".localizedFormat(httpResponse.statusCode))
                        return
                    }
                    
                    // 打印HTTP响应
                    if let httpResponse = response as? HTTPURLResponse {
                        print("DEBUG: HTTP 状态码: \(httpResponse.statusCode)")
                        print("DEBUG: HTTP 头信息: \(httpResponse.allHeaderFields)")
                    }
                    
                    // 打印服务器返回的原始数据（仅在调试时使用）
                    if let jsonString = String(data: data, encoding: .utf8) {
                        print("DEBUG: 原始响应数据: \(jsonString)")
                    }
                    
                    do {
                        let decoder = JSONDecoder()
                        
                        do {
                            let response = try decoder.decode(IntentFeedResponse.self, from: data)
                            print("DEBUG: 解码成功! 状态码: \(response.code)")
                            
                            if response.code == 200, let feedData = response.data {
                                print("DEBUG: 获取到\(feedData.list.count)条意图数据")
                                
                                let newFeeds = feedData.list.map { $0.toFeedPost() }
                                print("DEBUG: 成功转换\(newFeeds.count)条数据为FeedPost")
                                
                                // 预处理图片URL
                                let processedFeeds = newFeeds.map { post in
                                    // 如果images字段不为空，预先验证URL的有效性
                                    if let imagesString = post.images,
                                       let imageData = imagesString.data(using: .utf8),
                                       let imageArray = try? JSONDecoder().decode([String].self, from: imageData) {
                                        // 只保留有效的图片URL
                                        let validImages = imageArray.filter { !$0.isEmpty }
                                        if !validImages.isEmpty {
                                            // 使用 with 方法创建新的实例
                                            if let newImagesString = try? String(data: JSONEncoder().encode(validImages), encoding: .utf8) {
                                                return post.with(images: newImagesString)
                                            }
                                        }
                                    }
                                    return post.with(images: nil)
                                }
                                
                                if self.currentPage == 1 {
                                    self.feeds = processedFeeds
                                } else {
                                    self.feeds.append(contentsOf: processedFeeds)
                                }
                                
                                if processedFeeds.isEmpty {
                                    self.showEmptyMessage("暂无意图动态".localized)
                                } else {
                                    self.tableView.backgroundView = nil
                                }
                                
                                self.tableView.reloadData()
                                self.handleLoadingCompletion()
                                print("DEBUG: 数据加载完成，UI已更新")
                            } else {
                                print("DEBUG: 响应中状态码非200或data字段为空")
                                self.handleLoadingCompletion()
                                let message = "加载失败: %@".localizedFormat(response.message)
                                self.showErrorMessage(message)
                            }
                        } catch let decodeError as DecodingError {
                            // 详细打印解码错误
                            print("DEBUG: 解码错误详情:")
                            switch decodeError {
                            case .keyNotFound(let key, let context):
                                print("DEBUG: 找不到键: \(key), 路径: \(context.codingPath)")
                                print("DEBUG: 详细描述: \(context.debugDescription)")
                            case .valueNotFound(let type, let context):
                                print("DEBUG: 找不到值类型: \(type), 路径: \(context.codingPath)")
                                print("DEBUG: 详细描述: \(context.debugDescription)")
                            case .typeMismatch(let type, let context):
                                print("DEBUG: 类型不匹配: 期望 \(type), 路径: \(context.codingPath)")
                                print("DEBUG: 详细描述: \(context.debugDescription)")
                            case .dataCorrupted(let context):
                                print("DEBUG: 数据损坏: \(context)")
                                print("DEBUG: 详细描述: \(context.debugDescription)")
                            @unknown default:
                                print("DEBUG: 未知解码错误: \(decodeError)")
                            }
                            
                            // 尝试使用替代方法解析
                            self.tryAlternativeResponseFormat(data: data)
                        } catch {
                            print("DEBUG: 其他解码错误: \(error)")
                            self.handleLoadingCompletion()
                            self.showErrorMessage("数据解析失败，请稍后重试".localized)
                        }
                    }
                }
            }.resume()
        }
        
        // 开始首次请求
        performRequest()
    }
    
    private func loadSubscriptionFeeds() {
        // 检查SDK连接状态，避免在连接不稳定时发起网络请求
        let connectionStatus = IMController.shared.connectionRelay.value
        if connectionStatus != .syncComplete && connectionStatus != .connected {
            print("⚠️ SDK连接不稳定(\(connectionStatus))，延迟加载Subscription feeds")
            handleLoadingCompletion()
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.loadFeeds()
            }
            return
        }

        // 使用当前用户ID
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/feed?page=\(currentPage)&page_size=20&user_id=\(currentUserId)") else {
            handleLoadingCompletion()
            return
        }

        print("Loading subscription feeds from URL: \(url)")
        print("DEBUG: SDK连接状态: \(connectionStatus)")
        
        // 创建URLRequest并设置更长的超时时间
        var request = URLRequest(url: url)
        request.timeoutInterval = 30 // 增加超时时间到30秒
        
        // 使用重试机制
        func performRequest(retryCount: Int = 0, maxRetries: Int = 2) {
            URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    // 检查是否需要重试
                    if let error = error {
                        print("Error fetching feeds: \(error.localizedDescription)")
                        
                        // 如果是网络错误且还有重试次数，则重试
                        let isNetworkError = (error as NSError).domain == NSURLErrorDomain && 
                                            [NSURLErrorTimedOut, NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost].contains((error as NSError).code)
                        
                        if isNetworkError && retryCount < maxRetries {
                            print("重试订阅动态请求，第\(retryCount + 1)次...")
                            // 延迟1秒后重试
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                performRequest(retryCount: retryCount + 1, maxRetries: maxRetries)
                            }
                            return
                        }
                        
                        self.handleLoadingCompletion()
                        self.showErrorMessage("网络连接问题，请检查网络后重试".localized)
                        return
                    }
                    
                    guard let data = data else {
                        self.handleLoadingCompletion()
                        self.showErrorMessage("未收到数据".localized)
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode != 200 {
                        self.handleLoadingCompletion()
                        self.showErrorMessage("服务器返回错误: %d".localizedFormat(httpResponse.statusCode))
                        return
                    }
                    
                    do {
                        let decoder = JSONDecoder()
                        let feedResponse = try decoder.decode(FeedResponse.self, from: data)
                        
                        if feedResponse.status == "success" {
                            let newFeeds = feedResponse.posts
                            
                            // 预处理图片URL
                            let processedFeeds = newFeeds.map { post in
                                // 如果images字段不为空，预先验证URL的有效性
                                if let imagesString = post.images,
                                   let imageData = imagesString.data(using: .utf8),
                                   let imageArray = try? JSONDecoder().decode([String].self, from: imageData) {
                                    // 只保留有效的图片URL
                                    let validImages = imageArray.filter { !$0.isEmpty }
                                    if !validImages.isEmpty {
                                        // 使用 with 方法创建新的实例
                                        if let newImagesString = try? String(data: JSONEncoder().encode(validImages), encoding: .utf8) {
                                            return post.with(images: newImagesString)
                                        }
                                    }
                                }
                                return post.with(images: nil)
                            }
                            
                            if self.currentPage == 1 {
                                self.feeds = processedFeeds
                            } else {
                                self.feeds.append(contentsOf: processedFeeds)
                            }
                            
                            if processedFeeds.isEmpty {
                                self.showEmptyMessage("暂无订阅动态".localized)
                            } else {
                                self.tableView.backgroundView = nil
                            }
                            
                            self.tableView.reloadData()
                            self.handleLoadingCompletion()
                            print("成功加载 \(processedFeeds.count) 条动态")
                        } else {
                            self.handleLoadingCompletion()
                            self.showErrorMessage("加载失败".localized)
                        }
                    } catch {
                        print("Error decoding response: \(error)")
                        self.handleLoadingCompletion()
                        self.showErrorMessage("数据解析错误".localized)
                    }
                }
            }.resume()
        }
        
        // 开始首次请求
        performRequest()
    }
    
    private func handleLoadingCompletion() {
        isLoading = false
        loadingView.stopAnimating()
        loadingView.removeFromSuperview()
        tableView.refreshControl?.endRefreshing()
    }
    
    @objc private func segmentChanged() {
        // 更新下划线位置
        moveUnderline()
        
        // 立即清空当前数据并刷新界面
        feeds = []
        tableView.reloadData()
        
        // 重置页码
        currentPage = 1
        
        // 显示加载动画
        view.addSubview(loadingView)
        loadingView.center = view.center
        loadingView.startAnimating()
        
        // 延迟一帧再加载数据，确保UI已更新
        DispatchQueue.main.async {
            self.loadFeeds()
        }
    }
    
    // MARK: - Mock Data
    private func loadMockData() {
        DispatchQueue.main.async {
            let mockFeeds = [
                FeedPost(
                    id: 1,
                    publisherId: 123,
                    authorName: "测试用户1",
                    publishTime: "2024-03-14",
                    category: "分享生活",
                    content: "这是一条带图片的测试内容",
                    images: nil,
                    isLiked: 0,
                    likeCount: 30,
                    isFavorited: 0,
                    favoriteCount: 20,
                    repostCount: 10,
                    commentCount: 5
                ),
                FeedPost(
                    id: 2,
                    publisherId: 456,
                    authorName: "测试用户2",
                    publishTime: "2024-03-14",
                    category: "发表观点",
                    content: "这是第二条测试内容",
                    images: nil,
                    isLiked: 0,
                    likeCount: 25,
                    isFavorited: 0,
                    favoriteCount: 15,
                    repostCount: 5,
                    commentCount: 3
                )
            ]
            
            if self.currentPage == 1 {
                self.feeds = mockFeeds
            } else {
                self.feeds.append(contentsOf: mockFeeds)
            }
            self.tableView.reloadData()
        }
    }
    
    // MARK: - Underline
    private func addUnderlineForSelectedSegment() {
        // 移除旧的下划线
        underlineView?.removeFromSuperview()
        
        // 创建更细的下划线
        let underlineView = UIView()
        underlineView.backgroundColor = .black
        view.addSubview(underlineView)
        self.underlineView = underlineView
        
        // 设置下划线约束
        let segmentWidth = segmentedControl.frame.width / CGFloat(segmentedControl.numberOfSegments)
        // 根据选中的选项设置下划线宽度 - 进一步减小宽度
        let underlineWidth: CGFloat = segmentedControl.selectedSegmentIndex == 0 ? 25 : 35
        
        underlineView.snp.makeConstraints { make in
            make.bottom.equalTo(segmentedControl.snp.bottom)
            make.height.equalTo(2) // 下划线高度
            make.width.equalTo(underlineWidth)
            make.centerX.equalTo(segmentedControl.snp.left).offset(segmentWidth / 2 + CGFloat(segmentedControl.selectedSegmentIndex) * segmentWidth)
        }
    }
    
    private func moveUnderline() {
        let segmentWidth = segmentedControl.frame.width / CGFloat(segmentedControl.numberOfSegments)
        let centerX = segmentWidth / 2 + CGFloat(segmentedControl.selectedSegmentIndex) * segmentWidth
        
        UIView.animate(withDuration: 0.3) {
            self.underlineView?.snp.updateConstraints { make in
                make.centerX.equalTo(self.segmentedControl.snp.left).offset(centerX)
                // 根据选中的标签内容动态调整下划线宽度
                make.width.equalTo(self.segmentedControl.selectedSegmentIndex == 0 ? 25 : 35) // 进一步减小下划线宽度
            }
            self.view.layoutIfNeeded()
        }
    }
    
    // MARK: - Actions
    @objc private func handleSwipe(_ gesture: UISwipeGestureRecognizer) {
        if gesture.direction == .left && segmentedControl.selectedSegmentIndex == 0 {
            segmentedControl.selectedSegmentIndex = 1
            segmentChanged()
        } else if gesture.direction == .right && segmentedControl.selectedSegmentIndex == 1 {
            segmentedControl.selectedSegmentIndex = 0
            segmentChanged()
        }
    }
    
    // 修改 tryAlternativeResponseFormat 方法，避免直接加载模拟数据
    private func tryAlternativeResponseFormat(data: Data) {
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("DEBUG: 尝试替代解析 - JSON结构: \(Array(json.keys))")
                
                // 检查是否是错误响应
                if let code = json["code"] as? Int, code != 200 {
                    if let errorMessage = json["message"] as? String {
                        print("DEBUG: API返回错误: \(errorMessage)")
                        let message = "加载失败: %@".localizedFormat(errorMessage)
                        showErrorMessage(message)
                        return
                    }
                }
                
                // 检查data字段
                if let dataObj = json["data"] as? [String: Any] {
                    // 检查list字段 
                    if let list = dataObj["list"] as? [[String: Any]] {
                        print("DEBUG: 在'data.list'字段中找到\(list.count)条数据")
                        
                        // 这里可以尝试手动构建数据
                        // 但目前只显示消息，不尝试解析
                        showErrorMessage("数据结构已改变，请更新应用".localized)
                        return
                    }
                }
            }
        } catch {
            print("DEBUG: 替代解析失败: \(error)")
        }
        
        showErrorMessage("无法解析返回数据".localized)
    }
    
    // 添加显示错误信息的辅助方法
    private func showErrorMessage(_ message: String) {
        let errorLabel = UILabel()
        errorLabel.text = message
        errorLabel.textAlignment = .center
        errorLabel.textColor = .gray
        errorLabel.numberOfLines = 0
        errorLabel.font = UIFont(name: "Avenir-Book", size: 14) ?? UIFont.systemFont(ofSize: 14)
        
        tableView.backgroundView = errorLabel
        feeds = []
        tableView.reloadData()
    }
    
    // 添加显示空数据信息的辅助方法
    private func showEmptyMessage(_ message: String) {
        let emptyLabel = UILabel()
        emptyLabel.text = message
        emptyLabel.textAlignment = .center
        emptyLabel.textColor = .gray
        emptyLabel.font = UIFont(name: "Avenir-Book", size: 14) ?? UIFont.systemFont(ofSize: 14)
        
        tableView.backgroundView = emptyLabel
    }
    
    // 添加一个标志来跟踪下划线是否已初始化
    private var isUnderlinePositionInitialized = false
    
    // 添加一个方法来更新下划线位置
    private func updateUnderlinePosition() {
        let segmentWidth = segmentedControl.bounds.width / CGFloat(segmentedControl.numberOfSegments)
        let selectedIndex = segmentedControl.selectedSegmentIndex
        let centerX = segmentWidth * CGFloat(selectedIndex) + segmentWidth / 2
        
        UIView.animate(withDuration: 0.3) {
            self.underlineView?.snp.updateConstraints { make in
                make.centerX.equalTo(self.segmentedControl.snp.left).offset(centerX)
                // 根据选中的标签内容动态调整下划线宽度
                make.width.equalTo(selectedIndex == 0 ? 25 : 35) // 进一步减小下划线宽度
            }
            self.view.layoutIfNeeded()
        }
    }
    
    // 处理语言变更通知
    @objc private func handleLanguageChange() {
        // 打印详细的调试信息
        print("\n🔄 === 语言变更通知 ===")
        print("🌐 新的当前语言: \(Localize.currentLanguage())")
        print("🌍 系统语言: \(Locale.current.languageCode ?? "unknown")")
        print("💬 所有可用语言: \(Localize.availableLanguages())")
        
        // 检查意图动态文本
        let intentFeedText = "意图动态".localized
        print("📱 意图动态翻译后: \"\(intentFeedText)\"")
        
        // 更新UI元素的文本
        DispatchQueue.main.async {
            print("🔄 开始更新UI元素...")
            
            // 调用已经封装好的更新方法
            self.updateLocalizedTextForCurrentLanguage()
            
            print("🎉 UI更新完成")
        }
    }
}

// MARK: - UITableViewDelegate & UITableViewDataSource
extension IntentFeedViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return feeds.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: "FeedCell", for: indexPath) as? FeedCell else {
            return UITableViewCell()
        }
        
        let feed = feeds[indexPath.row]
        // 传递当前的动态类型和对应的缓存
        let isIntentFeed = segmentedControl.selectedSegmentIndex == 0
        cell.configure(with: feed, isIntentFeed: isIntentFeed, imageCache: isIntentFeed ? intentImageCache : subscriptionImageCache)
        cell.delegate = self
        
        // 设置用户资料点击代理
        cell.userProfileDelegate = self
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension // 自动计算高度
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 200 // 估计高度
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let post = feeds[indexPath.row]
        let detailVC = FeedDetailViewController(
            post: post.toContentDetail(),  // 确保这个方法返回正确的 ContentDetail 类型
            currentUserId: currentUserId,
            currentUserName: currentUserName
        )
        
        // 修复 fullScreen 的上下文问题
        detailVC.modalPresentationStyle = .fullScreen
        present(detailVC, animated: true)
    }
}

// MARK: - UIColor Extension
extension UIColor {
    convenience init(hex: String) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        
        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0
        
        self.init(red: red, green: green, blue: blue, alpha: 1.0)
    }
}

// MARK: - UILabel Padding Extension
private class PaddingLabel: UILabel {
    private var padding = UIEdgeInsets(top: 4, left: 8, bottom: 4, right: 8)
    
    override func drawText(in rect: CGRect) {
        super.drawText(in: rect.inset(by: padding))
    }
    
    override var intrinsicContentSize: CGSize {
        let size = super.intrinsicContentSize
        return CGSize(width: size.width + padding.left + padding.right,
                     height: size.height + padding.top + padding.bottom)
    }
}

// MARK: - FeedCell
class FeedCell: UITableViewCell {
    // MARK: - UI Components
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16 // 设置圆角为宽高的一半
        imageView.backgroundColor = UIColor(hex: "#F0F0F0") // 默认背景色
        imageView.isUserInteractionEnabled = true // 启用用户交互
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleAvatarTap))
        imageView.addGestureRecognizer(tapGesture)
        
        return imageView
    }()

    private lazy var contentContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private lazy var intentInfoStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 4
        stack.isHidden = true
        stack.distribution = .fill
        stack.alignment = .fill
        stack.translatesAutoresizingMaskIntoConstraints = false
        return stack
    }()

    lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.font = UIFont(name: "Avenir-Book", size: 15) ?? UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private lazy var buttonsStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .equalSpacing
        stack.spacing = 20
        return stack
    }()
    
    lazy var authorNameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont(name: "Avenir-Medium", size: 15) ?? UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = .black
        label.isUserInteractionEnabled = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleNameTap))
        label.addGestureRecognizer(tapGesture)
        
        return label
    }()
    
    lazy var imageContainerView: UIView = {
        let view = UIView()
        view.clipsToBounds = true
        view.backgroundColor = .clear // 移除默认背景色
        return view
    }()
    
    lazy var feedImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()
    
    private let statsView = UIStackView()
    private var imageCache: NSCache<NSString, UIImage>?
    private var isIntentFeed: Bool = false
    
    private var imageLoadingTask: URLSessionDataTask?
    private var currentImageURL: String?
    
    lazy var imageLoadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        indicator.color = .gray
        return indicator
    }()
    
    lazy var imageRetryButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("加载失败，点击重试".localized, for: .normal)
        button.titleLabel?.font = UIFont(name: "Avenir-Book", size: 14) ?? UIFont.systemFont(ofSize: 14)
        button.setTitleColor(.gray, for: .normal)
        button.addTarget(self, action: #selector(retryLoadImage), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    // 添加代理属性
    weak var delegate: FeedCellDelegate?
    
    // 添加色块容器视图
    private lazy var colorBlocksContainer: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 12
        view.clipsToBounds = true
        view.backgroundColor = .white
        return view
    }()
    
    // 色块视图数组
    private var colorBlocks: [UIView] = []
    
    private var heightConstraint: Constraint?
    private var stackHeightConstraint: Constraint?
    
    private lazy var mainContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var contentStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 12
        stack.alignment = .fill
        stack.distribution = .fill
        return stack
    }()
    
    private var buttonsStackTopConstraint: Constraint? // 添加约束引用
    
    weak var userProfileDelegate: FeedCellUserProfileDelegate?
    private var publisherId: Int = 0
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加主容器
        contentView.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 6, left: 16, bottom: 6, right: 16))
        }
        
        // 创建顶部容器视图（头像和作者名）
        let topContainer = UIView()
        mainContainer.addSubview(topContainer)
        
        // 添加头像和作者名到顶部容器
        topContainer.addSubview(avatarImageView)
        topContainer.addSubview(authorNameLabel)
        
        // 设置顶部容器约束
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(36)
        }
        
        // 设置头像约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
        
        // 设置作者名约束
        authorNameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalToSuperview()
        }
        
        // 添加主内容容器
        let contentContainer = UIView()
        mainContainer.addSubview(contentContainer)
        
        // 配置内容栈视图
        contentContainer.addSubview(contentStackView)
        
        // 配置内容标签
        let textContainer = UIView()
        contentLabel.numberOfLines = 0
        contentLabel.setContentCompressionResistancePriority(.required, for: .vertical)
        contentLabel.setContentHuggingPriority(.required, for: .vertical)
        
        textContainer.addSubview(contentLabel)
        contentLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 12, left: 12, bottom: 8, right: 12))
        }
        contentStackView.addArrangedSubview(textContainer)
        
        // 配置图片容器和图片视图
        imageContainerView.clipsToBounds = true
        imageContainerView.addSubview(feedImageView)
        feedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 配置意图信息栈
        intentInfoStack.spacing = 4
        intentInfoStack.isLayoutMarginsRelativeArrangement = true
        intentInfoStack.layoutMargins = UIEdgeInsets(top: 12, left: 12, bottom: 8, right: 12)
        contentStackView.addArrangedSubview(intentInfoStack)
        
        // 添加底部操作栏
        mainContainer.addSubview(buttonsStack)
        
        // 设置内容栈视图约束
        contentStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置内容容器约束
        contentContainer.snp.makeConstraints { make in
            make.top.equalTo(topContainer.snp.bottom)
            make.left.right.equalToSuperview()
        }
        
        // 设置底部操作栏约束
        buttonsStack.snp.makeConstraints { make in
            make.top.equalTo(contentContainer.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().offset(-6)
            make.height.equalTo(40)
        }
        
        // 设置图片加载相关的视图
        setupImageContainer()
    }
    
    private func setupImageContainer() {
        imageContainerView.addSubview(imageLoadingIndicator)
        imageContainerView.addSubview(imageRetryButton)
        imageContainerView.addSubview(feedImageView)
        
        imageLoadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        imageRetryButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        feedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置默认背景色为浅灰色作为占位图
        imageContainerView.backgroundColor = UIColor(hex: "#F5F5F5")
    }
    
    private func addImageContainer() {
        // 如果已经添加过，就不重复添加
        if imageContainerView.superview == contentStackView {
            return
        }
        
        print("添加图片容器")
        contentStackView.insertArrangedSubview(imageContainerView, at: 1)
        
        imageContainerView.snp.remakeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(200)
        }
        
        // 调整图片容器的上下间距
        if let index = contentStackView.arrangedSubviews.firstIndex(of: imageContainerView) {
            contentStackView.setCustomSpacing(16, after: contentStackView.arrangedSubviews[index - 1]) // 内容标题和图片之间的间距
            if index + 1 < contentStackView.arrangedSubviews.count {
                contentStackView.setCustomSpacing(16, after: imageContainerView) // 图片和意图信息之间的间距
            }
        }
    }
    
    private func removeImageContainer() {
        print("移除图片容器")
        imageLoadingTask?.cancel()
        imageLoadingTask = nil
        currentImageURL = nil
        imageLoadingIndicator.stopAnimating()
        imageRetryButton.isHidden = true
        imageContainerView.removeFromSuperview()
        feedImageView.image = nil
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    func configure(with post: FeedPost, isIntentFeed: Bool, imageCache: NSCache<NSString, UIImage>) {
        self.imageCache = imageCache
        self.isIntentFeed = isIntentFeed
        
        // 重置图片相关状态
        imageLoadingTask?.cancel()
        imageLoadingTask = nil
        currentImageURL = nil
        feedImageView.image = nil
        
        // 设置基础内容
        authorNameLabel.text = post.authorName

        // 设置默认头像（避免触发网络请求导致退出登录）
        avatarImageView.image = UIImage(nameInBundle: "contact_my_friend_icon")
        
        // 设置内容文本
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 2
        contentLabel.attributedText = NSAttributedString(
            string: post.content,
            attributes: [
                .paragraphStyle: paragraphStyle,
                .font: UIFont.systemFont(ofSize: 15)
            ]
        )
        
        // 处理图片显示
        if let imageURLs = post.imageURLs, let firstImageURL = imageURLs.first {
            print("处理图片显示 - 有图片")
            print("图片URL: \(firstImageURL.absoluteString)")
            print("动态类型: \(isIntentFeed ? "意图动态".localized : "订阅动态".localized)")
            
            // 添加图片容器并开始加载
            addImageContainer()
            loadImage(from: firstImageURL.absoluteString)
        } else {
            print("处理图片显示 - 无图片")
            removeImageContainer()
        }
        
        // 设置意图信息
        setupIntentInfo(for: post)
        
        // 根据是否有意图信息调整底部按钮栈的间距
        buttonsStack.snp.updateConstraints { make in
            make.top.equalTo(contentStackView.superview!.snp.bottom).offset(intentInfoStack.arrangedSubviews.isEmpty ? 8 : 12)
        }
        
        // 更新底部操作栏
        updateActionButtons(post)
        
        setNeedsLayout()
        layoutIfNeeded()
        
        // 保存当前feed的发布者ID，供点击时使用
        self.publisherId = post.publisherId
    }

    private func loadImage(from urlString: String) {
        print("开始加载图片: \(urlString)")
        print("动态类型: \(isIntentFeed ? "意图动态".localized : "订阅动态".localized)")
        
        // 如果是相同的URL，不重复加载
        if currentImageURL == urlString {
            return
        }
        
        // 取消之前的加载任务
        imageLoadingTask?.cancel()
        currentImageURL = urlString
        
        guard let url = URL(string: urlString) else {
            print("无效的图片URL")
            showImageLoadError()
            return
        }
        
        // 先检查对应类型的缓存
        if let cache = imageCache, let cachedImage = cache.object(forKey: urlString as NSString) {
            print("使用缓存图片")
            showLoadedImage(cachedImage)
            return
        }
        
        // 显示加载状态
        showImageLoading()
        
        // 创建新的加载任务
        let task = URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                // 检查是否是当前cell正在加载的图片
                guard self.currentImageURL == urlString else {
                    return
                }
                
                if let error = error {
                    print("图片加载失败: \(error.localizedDescription)")
                    self.showImageLoadError()
                    return
                }
                
                guard let data = data, let image = UIImage(data: data) else {
                    print("图片数据无效")
                    self.showImageLoadError()
                    return
                }
                
                // 缓存图片到对应类型的缓存中
                self.imageCache?.setObject(image, forKey: urlString as NSString)
                
                // 显示图片
                self.showLoadedImage(image)
            }
        }
        
        imageLoadingTask = task
        task.resume()
    }
    
    private func showImageLoading() {
        addImageContainer()
        imageRetryButton.isHidden = true
        imageLoadingIndicator.startAnimating()
    }
    
    private func showLoadedImage(_ image: UIImage) {
        imageLoadingIndicator.stopAnimating()
        imageRetryButton.isHidden = true
        feedImageView.image = image
        
        // 添加淡入动画
        feedImageView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            self.feedImageView.alpha = 1
        }
    }
    
    private func showImageLoadError() {
        imageLoadingIndicator.stopAnimating()
        imageRetryButton.isHidden = false
    }
    
    @objc private func retryLoadImage() {
        guard let urlString = currentImageURL else { return }
        imageRetryButton.isHidden = true
        loadImage(from: urlString)
    }

    private func setupIntentInfo(for post: FeedPost) {
        intentInfoStack.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        guard let intent = post.intent else {
            intentInfoStack.isHidden = true
            return
        }
        
        intentInfoStack.isHidden = false
        
        // 创建意图标签
        let intentLabel = createInfoLabel(text: "\("意图：".localized)\(intent)", color: .darkGray)
        intentInfoStack.addArrangedSubview(intentLabel)
        
        // 创建匹配度标签
        if let score = post.matchScore {
            let scoreLabel = createInfoLabel(
                text: "匹配度：".localized + String(format: "%.1f%%", score),
                color: UIColor(hex: "#4A90E2")
            )
            intentInfoStack.addArrangedSubview(scoreLabel)
        }
        
        // 创建匹配原因标签
        if let reason = post.matchReason, !reason.isEmpty {
            let reasonLabel = createInfoLabel(text: "\("匹配原因：".localized)\(reason)", color: .gray)
            intentInfoStack.addArrangedSubview(reasonLabel)
        }
        
        // 添加额外的底部间距
        let spacerView = UIView()
        spacerView.snp.makeConstraints { make in
            make.height.equalTo(4)
        }
        intentInfoStack.addArrangedSubview(spacerView)
    }

    private func createInfoLabel(text: String, color: UIColor) -> UILabel {
        let label = UILabel()
        
        // 直接使用传入的本地化文本
        label.text = text
        label.font = UIFont(name: "Avenir-Book", size: 12) ?? UIFont.systemFont(ofSize: 12)
        label.textColor = color
        label.numberOfLines = text.contains("匹配原因：".localized) ? 0 : 1
        
        label.setContentCompressionResistancePriority(.required, for: .vertical)
        label.setContentHuggingPriority(.required, for: .vertical)
        
        if !text.contains("匹配原因：".localized) {
            label.snp.makeConstraints { make in
                make.height.equalTo(14) // 减小固定高度
            }
        }
        
        return label
    }

    private func updateActionButtons(_ post: FeedPost) {
        // 清除旧的按钮
        buttonsStack.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 更新底部操作栏
        let buttons = [
            ("评论".localized, "ping_icon", post.commentCount),
            ("转发".localized, "zhuan_icon", post.repostCount),
            ("点赞".localized, post.isLiked == 1 ? "zan_selected" : "zan", post.likeCount),
            ("收藏".localized, post.isFavorited == 1 ? "cang_selected" : "cang_icon", post.favoriteCount)
        ]
        
        buttons.forEach { (title, iconName, count) in
            let button = createStatsItem(title: title, icon: iconName, count: count)
            buttonsStack.addArrangedSubview(button)
        }
    }
    
    private func createStatsItem(title: String, icon: String, count: Int) -> UIView {
        let container = UIView()
        container.isUserInteractionEnabled = true
        
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        // 使用原始图标名称
        if let image = UIImage(nameInBundle: icon) {
            imageView.image = image
        }
        container.addSubview(imageView)
        
        let label = UILabel()
        label.text = "\(count)"
        label.font = .systemFont(ofSize: 14)
        label.textColor = .gray
        container.addSubview(label)
        
        imageView.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
            make.size.equalTo(CGSize(width: 20, height: 20))
        }
        
        label.snp.makeConstraints { make in
            make.left.equalTo(imageView.snp.right).offset(4)
            make.centerY.right.equalToSuperview()
        }
        
        // 如果是点赞按钮，添加点击手势
        if title == "点赞".localized {
            let tap = UITapGestureRecognizer(target: self, action: #selector(handleLikeTap(_:)))
            container.addGestureRecognizer(tap)
        }
        
        // 如果是收藏按钮，添加点击手势
        if title == "收藏".localized {
            let tap = UITapGestureRecognizer(target: self, action: #selector(handleFavoriteTap(_:)))
            container.addGestureRecognizer(tap)
        }
        
        return container
    }
    
    @objc private func handleLikeTap(_ gesture: UITapGestureRecognizer) {
        delegate?.didTapLike(self)
    }
    
    @objc private func handleFavoriteTap(_ gesture: UITapGestureRecognizer) {
        delegate?.didTapFavorite(self)
    }

    func updateLikeStatus(isLiked: Bool, count: Int) {
        // 找到点赞按钮（通常是第三个按钮）
        if let likeButton = buttonsStack.arrangedSubviews[safe: 2] {
            // 更新图标
            if let imageView = likeButton.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
                imageView.image = UIImage(nameInBundle: isLiked ? "zan_selected" : "zan")
            }
            // 更新数字
            if let label = likeButton.subviews.first(where: { $0 is UILabel }) as? UILabel {
                label.text = "\(count)"
            }
        }
    }
    
    func updateFavoriteStatus(isFavorited: Bool, count: Int) {
        // 找到收藏按钮（通常是第四个按钮）
        if let favoriteButton = buttonsStack.arrangedSubviews[safe: 3] {
            // 更新图标
            if let imageView = favoriteButton.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
                imageView.image = UIImage(nameInBundle: isFavorited ? "cang_selected" : "cang_icon")
            }
            // 更新数字
            if let label = favoriteButton.subviews.first(where: { $0 is UILabel }) as? UILabel {
                label.text = "\(count)"
            }
        }
    }
    
    @objc private func handleAvatarTap() {
        print("头像被点击，发布者ID: \(publisherId)")
        userProfileDelegate?.didTapUserAvatar(self, publisherId: publisherId)
    }
    
    @objc private func handleNameTap() {
        print("用户名被点击，发布者ID: \(publisherId)")
        userProfileDelegate?.didTapUserName(self, publisherId: publisherId)
    }

}

// 添加代理协议
protocol FeedCellDelegate: AnyObject {
    func didTapLike(_ cell: FeedCell)
    func didTapFavorite(_ cell: FeedCell)
}

// 在 IntentFeedViewController 中实现代理方法
extension IntentFeedViewController: FeedCellDelegate {
    func didTapLike(_ cell: FeedCell) {
        guard let indexPath = tableView.indexPath(for: cell) else { return }
        let post = feeds[indexPath.row]
        
        if post.isLiked == 1 {
            unlikePost(post, at: indexPath, cell: cell)
        } else {
            likePost(post, at: indexPath, cell: cell)
        }
    }
    
    func didTapFavorite(_ cell: FeedCell) {
        guard let indexPath = tableView.indexPath(for: cell) else { return }
        let post = feeds[indexPath.row]
        
        if post.isFavorited == 1 {
            unfavoritePost(post, at: indexPath, cell: cell)
        } else {
            favoritePost(post, at: indexPath, cell: cell)
        }
    }
    
    private func likePost(_ post: FeedPost, at indexPath: IndexPath, cell: FeedCell) {
        print("Attempting to like post: \(post.id)")
        
        let request = [
            "post_id": post.id,
            "user_id": currentUserId,
            "user_name": currentUserName
        ] as [String : Any]
        
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/like"),
              let jsonData = try? JSONSerialization.data(withJSONObject: request) else {
            print("Failed to create request")
            return
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = jsonData
        
        URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            if let error = error {
                print("Like request failed: \(error)")
                return
            }
            
            guard let data = data else {
                print("No data received")
                return
            }
            
            do {
                let response = try JSONDecoder().decode(LikeResponse.self, from: data)
                print("Like response: \(response)")
                
                if response.status == "success" {
                    DispatchQueue.main.async {
                        // 更新数据模型
                        let updatedPost = post.with(isLiked: 1, likeCount: post.likeCount + 1)
                        self?.feeds[indexPath.row] = updatedPost
                        
                        // 直接更新单元格的点赞状态，而不是重新加载整个单元格
                        cell.updateLikeStatus(isLiked: true, count: updatedPost.likeCount)
                    }
                }
            } catch {
                print("Failed to decode response: \(error)")
            }
        }.resume()
    }
    
    private func unlikePost(_ post: FeedPost, at indexPath: IndexPath, cell: FeedCell) {
        print("Attempting to unlike post: \(post.id)")
        
        let request = [
            "post_id": post.id,
            "user_id": currentUserId
        ] as [String : Any]
        
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/unlike"),
              let jsonData = try? JSONSerialization.data(withJSONObject: request) else {
            print("Failed to create request")
            return
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = jsonData
        
        URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            if let error = error {
                print("Unlike request failed: \(error)")
                return
            }
            
            guard let data = data else {
                print("No data received")
                return
            }
            
            do {
                let response = try JSONDecoder().decode(LikeResponse.self, from: data)
                print("Unlike response: \(response)")
                
                if response.status == "success" {
                    DispatchQueue.main.async {
                        // 更新数据模型
                        let updatedPost = post.with(isLiked: 0, likeCount: post.likeCount - 1)
                        self?.feeds[indexPath.row] = updatedPost
                        
                        // 直接更新单元格的点赞状态，而不是重新加载整个单元格
                        cell.updateLikeStatus(isLiked: false, count: updatedPost.likeCount)
                    }
                }
            } catch {
                print("Failed to decode response: \(error)")
            }
        }.resume()
    }
    
    private func favoritePost(_ post: FeedPost, at indexPath: IndexPath, cell: FeedCell) {
        print("Attempting to favorite post: \(post.id)")
        
        let request = [
            "post_id": post.id,
            "user_id": currentUserId,
            "user_name": currentUserName
        ] as [String : Any]
        
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/favorite"),
              let jsonData = try? JSONSerialization.data(withJSONObject: request) else {
            print("Failed to create request")
            return
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = jsonData
        
        URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            if let error = error {
                print("Favorite request failed: \(error)")
                return
            }
            
            guard let data = data else {
                print("No data received")
                return
            }
            
            do {
                let response = try JSONDecoder().decode(FavoriteResponse.self, from: data)
                print("Favorite response: \(response)")
                
                if response.status == "success" {
                    DispatchQueue.main.async {
                        // 更新数据模型
                        let updatedPost = post.with(isFavorited: 1, favoriteCount: post.favoriteCount + 1)
                        self?.feeds[indexPath.row] = updatedPost
                        
                        // 直接更新单元格的收藏状态
                        cell.updateFavoriteStatus(isFavorited: true, count: updatedPost.favoriteCount)
                    }
                }
            } catch {
                print("Failed to decode response: \(error)")
            }
        }.resume()
    }
    
    private func unfavoritePost(_ post: FeedPost, at indexPath: IndexPath, cell: FeedCell) {
        print("Attempting to unfavorite post: \(post.id)")
        
        let request = [
            "post_id": post.id,
            "user_id": currentUserId
        ] as [String : Any]
        
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/unfavorite"),
              let jsonData = try? JSONSerialization.data(withJSONObject: request) else {
            print("Failed to create request")
            return
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = jsonData
        
        URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            if let error = error {
                print("Unfavorite request failed: \(error)")
                return
            }
            
            guard let data = data else {
                print("No data received")
                return
            }
            
            do {
                let response = try JSONDecoder().decode(FavoriteResponse.self, from: data)
                print("Unfavorite response: \(response)")
                
                if response.status == "success" {
                    DispatchQueue.main.async {
                        // 更新数据模型
                        let updatedPost = post.with(isFavorited: 0, favoriteCount: post.favoriteCount - 1)
                        self?.feeds[indexPath.row] = updatedPost
                        
                        // 直接更新单元格的收藏状态
                        cell.updateFavoriteStatus(isFavorited: false, count: updatedPost.favoriteCount)
                    }
                }
            } catch {
                print("Failed to decode response: \(error)")
            }
        }.resume()
    }
}

// 添加安全下标访问
extension Collection {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - TabBar Item Setup
extension IntentFeedViewController {
    func setupTabBarItem() {
        let currentLanguage = Localize.currentLanguage()
        let tabTitle = currentLanguage == "en" ? "Chill Moment" : "Chill时刻"
        
        tabBarItem = UITabBarItem(
            title: tabTitle,
            image: UIImage(named: "tab_feed")?.withRenderingMode(.alwaysOriginal),
            selectedImage: UIImage(named: "tab_feed_selected")?.withRenderingMode(.alwaysOriginal)
        )
        
        let normalAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 10),
            .foregroundColor: UIColor(hex: "#999999")
        ]
        
        let selectedAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 10),
            .foregroundColor: UIColor.black
        ]
        
        tabBarItem.setTitleTextAttributes(normalAttributes, for: .normal)
        tabBarItem.setTitleTextAttributes(selectedAttributes, for: .selected)
    }
}

// 添加用户资料点击代理协议
protocol FeedCellUserProfileDelegate: AnyObject {
    func didTapUserAvatar(_ cell: FeedCell, publisherId: Int)
    func didTapUserName(_ cell: FeedCell, publisherId: Int)
}

// 在IntentFeedViewController中实现用户头像和名称点击代理
extension IntentFeedViewController: FeedCellUserProfileDelegate {
    func didTapUserAvatar(_ cell: FeedCell, publisherId: Int) {
        navigateToUserProfile(publisherId: publisherId)
    }
    
    func didTapUserName(_ cell: FeedCell, publisherId: Int) {
        navigateToUserProfile(publisherId: publisherId)
    }
    
    private func navigateToUserProfile(publisherId: Int) {
        // 将Int类型的publisherId转换为String
        let userIdString = String(publisherId)
        
        // 创建并显示用户资料页面
        let userDetailVC = OUIIM.UserDetailTableViewController(userId: userIdString, groupId: nil)
        navigationController?.pushViewController(userDetailVC, animated: true)
    }
}
