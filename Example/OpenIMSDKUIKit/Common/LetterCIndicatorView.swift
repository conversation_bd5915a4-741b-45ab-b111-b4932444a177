import UIKit

// 本地字母C指示器视图，用于登录加载状态
public class LetterCIndicatorView: UIView {
    private var letterLabel: UILabel!
    private var _isAnimating = false

    public var isAnimating: Bool {
        return _isAnimating
    }
    
    // 配置选项
    public struct Configuration {
        let fontSize: CGFloat
        let textColor: UIColor
        let size: CGSize
        
        public init(fontSize: CGFloat = 16, textColor: UIColor = .black, size: CGSize = CGSize(width: 24, height: 24)) {
            self.fontSize = fontSize
            self.textColor = textColor
            self.size = size
        }
        
        // 预设配置
        public static let `default` = Configuration()
        public static let small = Configuration(fontSize: 12, textColor: .black, size: CGSize(width: 16, height: 16))
        public static let medium = Configuration(fontSize: 16, textColor: .black, size: CGSize(width: 24, height: 24))
        public static let large = Configuration(fontSize: 20, textColor: .black, size: CGSize(width: 32, height: 32))
        public static let video = Configuration(fontSize: 16, textColor: .white, size: CGSize(width: 24, height: 24))
    }
    
    private var config: Configuration
    
    public init(configuration: Configuration = .default) {
        self.config = configuration
        super.init(frame: CGRect(origin: .zero, size: configuration.size))
        setupLetterC()
    }
    
    public override init(frame: CGRect) {
        self.config = .default
        super.init(frame: frame)
        setupLetterC()
    }
    
    public required init?(coder: NSCoder) {
        self.config = .default
        super.init(coder: coder)
        setupLetterC()
    }
    
    private func setupLetterC() {
        letterLabel = UILabel()
        letterLabel.text = "C"
        letterLabel.textColor = config.textColor
        letterLabel.font = UIFont(name: "Avenir-Heavy", size: config.fontSize) ?? UIFont.boldSystemFont(ofSize: config.fontSize)
        letterLabel.textAlignment = .center
        letterLabel.alpha = 0.6
        
        // 确保标签不被裁剪
        letterLabel.clipsToBounds = false
        letterLabel.backgroundColor = UIColor.clear
        
        addSubview(letterLabel)
        
        // 设置约束
        letterLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            letterLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            letterLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            letterLabel.widthAnchor.constraint(equalTo: widthAnchor),
            letterLabel.heightAnchor.constraint(equalTo: heightAnchor)
        ])
    }
    
    public func startAnimating() {
        guard !_isAnimating else { return }
        _isAnimating = true
        
        let pulseAnimation = CAKeyframeAnimation(keyPath: "opacity")
        pulseAnimation.values = [0.6, 1.0, 0.6]
        pulseAnimation.keyTimes = [0.0, 0.5, 1.0]
        pulseAnimation.duration = 0.8
        pulseAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        pulseAnimation.repeatCount = .infinity
        
        letterLabel.layer.add(pulseAnimation, forKey: "letterPulse")
    }
    
    public func stopAnimating() {
        guard _isAnimating else { return }
        _isAnimating = false
        
        letterLabel.layer.removeAllAnimations()
        letterLabel.alpha = 0.6
    }
    
    public override var intrinsicContentSize: CGSize {
        return config.size
    }
}
